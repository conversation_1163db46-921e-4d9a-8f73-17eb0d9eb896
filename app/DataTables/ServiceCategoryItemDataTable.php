<?php

namespace App\DataTables;

use App\Models\Admin\ServiceCategoryItem;
use Yajra\DataTables\Html\Button;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;

class ServiceCategoryItemDataTable extends DataTable
{
    /**
     * Build DataTable class.
     *
     * @param mixed $query Results from query() method.
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        return datatables()
            ->eloquent($query)
            ->setRowClass(function ($row) {
                return config('custom.custom.myStyle') == 'dark' ? 'bg-primary text-black' : '';
            })
            ->addColumn('actions', content: 'content.service_category_items.actions')
            ->editColumn('image', function ($row) {
                return $row->image ? '<img src="' . $row->image . '" alt="Image" class="img-thumbnail" width="50">' : 'No Image';
            })
            ->editColumn('video', function ($row) {
                return $row->video ? '<video src="' . $row->video . '" controls width="100"></video>' : 'No Video';
            })
            ->editColumn('active', function ($row) {
                $status = $row->active ? 'Active' : 'Inactive';
                $badgeClass = $row->active ? 'badge-success' : 'badge-danger';
                return '<span class="bgbadge ' . $badgeClass . '">' . __($status) . '</span>';
            })
            ->editColumn('user_id', function ($row) {
                return $row->user ? 'ID: ' . $row->user_id . ' - ' . $row->user->name : 'No User';
            })
            ->editColumn('created_at', function ($row) {
                return date('Y-m-d h:i A', strtotime($row->created_at));
            })
            ->rawColumns(['actions', 'image', 'video', 'active']);
    }

    /**
     * Get query source of dataTable.
     *
     * @param \App\Models\Admin\ServiceCategoryItem $model
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function query(ServiceCategoryItem $model)
    {
        return $model->with(['serviceCategory', 'user'])->newQuery();
    }

    /**
     * Get language file for datatable.
     *
     * @return string
     */
    public function getLang()
    {
        if (app()->getLocale() == 'en') {
            return asset('dashboard_files/plugins/dtable/English.json');
        } elseif (app()->getLocale() == 'ar') {
            return asset('dashboard_files/plugins/dtable/Arabic.json');
        }
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        return $this->builder()
            ->setTableId('service-category-items-table')
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->dom('Bfrtip')
            ->orderBy(10, 'desc')
            ->lengthMenu([[10, 25, 50, 100, -1], [10, 25, 50, 100, __('All')]])
            ->parameters([
                'dom' => 'Blfrtip',
            ])
            ->responsive(true)
            ->buttons(
                Button::make('excel')->text(__('Excel')),
                Button::make('print')->text(__('Print')),
            )->language(['url' => $this->getLang()]);
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::make('id')->addClass('text-center')->title('#'),
            Column::make('title')->addClass('text-center')->title(__('title')),
            Column::make('content')->addClass('text-center')->title(__('content')),
            Column::make('image')->addClass('text-center')->title(__('image')),
            Column::make('video')->addClass('text-center')->title(__('video')),
            Column::make('price')->addClass('text-center')->title(__('price')),
            Column::make('weekend_price')->addClass('text-center')->title(__('weekend_price')),
            Column::make('active')->addClass('text-center')->title(__('status')),
            Column::make('user_id')->addClass('text-center')->title(__('user')),
            Column::make('service_category.title_' . __('lang'))->defaultContent('')->addClass('text-center')->title(__('service_category')),
            Column::make('created_at')->addClass('text-center')->title(__('created_at')),
            Column::computed('actions')
                ->exportable(false)
                ->printable(false)
                ->addClass('text-center')->title(__('actions')),
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename(): string
    {
        return 'ServiceCategoryItems_' . date('YmdHis');
    }
}