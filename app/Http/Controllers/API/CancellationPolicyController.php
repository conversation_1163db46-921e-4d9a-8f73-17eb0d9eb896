<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\CancellationPolicyResource;
use App\Models\Admin\CancellationPolicy;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class CancellationPolicyController extends Controller
{
    /**
     * Display a listing of active cancellation policies.
     */
    public function index(): JsonResponse
    {
        try {
            $policies = CancellationPolicy::active()
                ->ordered()
                ->get();

            return response()->json([
                'success' => true,
                'message' => trans('api.data_retrieved_success'),
                'data' => CancellationPolicyResource::collection($policies),
            ], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => trans('api.something_went_wrong'),
                'data' => [],
            ], Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Display the specified cancellation policy.
     */
    public function show($id)
    {
        try {
            $policy = CancellationPolicy::find($id);

            if (!$policy) {
                return $this->ApiResponse(
                    false,
                    trans('api.not_found'),
                    [],
                    Response::HTTP_NOT_FOUND
                );
            }

            return $this->ApiResponse(
                true,
                trans('api.data_retrieved_success'),
                new CancellationPolicyResource($policy),
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_went_wrong'),
                [],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Store a newly created cancellation policy (Admin only).
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'name_en' => 'required|string|max:255',
                'name_ar' => 'required|string|max:255',
                'description_en' => 'required|string',
                'description_ar' => 'required|string',
                'policy_type' => 'required|in:flexible_short,moderate_short,strict_short,moderate_long,strict_long,custom',
                'duration_type' => 'required|in:short,long,both',
                'cancellation_window_hours' => 'required|integer|min:0',
                'refund_percentage' => 'required|numeric|min:0|max:100',
                'booking_window_hours' => 'nullable|integer|min:0',
                'minimum_notice_hours' => 'nullable|integer|min:0',
                'service_fee_refundable' => 'boolean',
                'cleaning_fee_refundable' => 'boolean',
                'is_active' => 'boolean',
                'order' => 'integer|min:0',
            ]);

            $policy = CancellationPolicy::create($request->all());

            return $this->ApiResponse(
                true,
                trans('api.created_success'),
                new CancellationPolicyResource($policy),
                Response::HTTP_CREATED
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                $e->getMessage(),
                [],
                Response::HTTP_BAD_REQUEST
            );
        }
    }

    /**
     * Update the specified cancellation policy (Admin only).
     */
    public function update(Request $request, $id)
    {
        try {
            $policy = CancellationPolicy::find($id);

            if (!$policy) {
                return $this->ApiResponse(
                    false,
                    trans('api.not_found'),
                    [],
                    Response::HTTP_NOT_FOUND
                );
            }

            $request->validate([
                'name_en' => 'string|max:255',
                'name_ar' => 'string|max:255',
                'description_en' => 'string',
                'description_ar' => 'string',
                'policy_type' => 'in:flexible_short,moderate_short,strict_short,moderate_long,strict_long,custom',
                'duration_type' => 'in:short,long,both',
                'cancellation_window_hours' => 'integer|min:0',
                'refund_percentage' => 'numeric|min:0|max:100',
                'booking_window_hours' => 'nullable|integer|min:0',
                'minimum_notice_hours' => 'nullable|integer|min:0',
                'service_fee_refundable' => 'boolean',
                'cleaning_fee_refundable' => 'boolean',
                'is_active' => 'boolean',
                'order' => 'integer|min:0',
            ]);

            $policy->update($request->all());

            return $this->ApiResponse(
                true,
                trans('api.updated_success'),
                new CancellationPolicyResource($policy),
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                $e->getMessage(),
                [],
                Response::HTTP_BAD_REQUEST
            );
        }
    }

    /**
     * Remove the specified cancellation policy (Admin only).
     */
    public function destroy($id)
    {
        try {
            $policy = CancellationPolicy::find($id);

            if (!$policy) {
                return $this->ApiResponse(
                    false,
                    trans('api.not_found'),
                    [],
                    Response::HTTP_NOT_FOUND
                );
            }

            // Check if policy is being used by any items
            if ($policy->items()->count() > 0) {
                return $this->ApiResponse(
                    false,
                    'Cannot delete policy that is being used by properties',
                    [],
                    Response::HTTP_CONFLICT
                );
            }

            $policy->delete();

            return $this->ApiResponse(
                true,
                trans('api.deleted_success'),
                [],
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->ApiResponse(
                false,
                trans('api.something_went_wrong'),
                [],
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}
