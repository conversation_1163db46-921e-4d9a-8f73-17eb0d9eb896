<?php
namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Resources\FacilitiesResource;
use App\Models\Admin\Facilities;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FacilitiesController extends Controller
{
    public function __construct()
    {
    }

    /**
     * Get facilities list.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function get_list(Request $request)
    {
        $list = Facilities::orderBy('order')->get();

        // Return standardized JSON response
        return response()->json([
            'success' => !empty($list),
            'message' => trans('api.' . (!empty($list) ? 'data_retrieved_success' : 'no_data_found')),
            'data' => FacilitiesResource::collection($list),
        ], !empty($list) ? 200 : 404);
    }
}
