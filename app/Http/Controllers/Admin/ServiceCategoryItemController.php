<?php
namespace App\Http\Controllers\Admin;

use App\DataTables\ServiceCategoryItemDataTable;
use App\Http\Controllers\Controller;
use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryForm;
use App\Models\Admin\ServiceCategoryItem;
use App\Models\AuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ServiceCategoryItemController extends Controller
{
    private const NAME   = 'service_category_items';
    private const NAME_S = 'service_category_item';

    public function __construct()
    {
        $this->middleware(['permission:read ' . self::NAME])->only('index');
        $this->middleware(['permission:create ' . self::NAME])->only('create');
        $this->middleware(['permission:update ' . self::NAME])->only('edit');
        $this->middleware(['permission:delete ' . self::NAME])->only('destroy');
    }

    /**
     * Display a listing of the resource.
     *
     * @param ServiceCategoryItemDataTable $dataTable
     * @return \Illuminate\Http\Response
     */
    public function index(ServiceCategoryItemDataTable $dataTable)
    {
        $name  = self::NAME;
        $nameS = self::NAME_S;
        return $dataTable->render('content.' . self::NAME . '.index', compact('name', 'nameS'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $name                = self::NAME;
        $nameS               = self::NAME_S;
        $serviceCategories   = ServiceCategory::all();
        $users               = User::all();
        $cities              = \App\Models\Admin\City::all();
        $propertyTypes       = \App\Models\Admin\PropertyType::all();
        $cancellationPolicies = \App\Models\Admin\CancellationPolicy::all();
        $serviceCategoryForms = ServiceCategoryForm::all();
        
        return view('content.' . self::NAME . '.create', compact(
            'name', 'nameS', 'serviceCategories', 'users', 'cities', 
            'propertyTypes', 'cancellationPolicies', 'serviceCategoryForms'
        ));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->validate([
            'title'                        => 'required|string',
            'content'                      => 'nullable|string',
            'image'                        => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'video'                        => 'nullable|file|mimetypes:video/mp4,video/quicktime|max:50000',
            'price'                        => 'required|numeric',
            'weekend_price'                => 'nullable|numeric',
            'week_price'                   => 'nullable|numeric',
            'month_price'                  => 'nullable|numeric',
            'lat'                          => 'nullable|numeric',
            'lon'                          => 'nullable|numeric',
            'address'                      => 'nullable|string',
            'active'                       => 'nullable|boolean',
            'confirmation'                 => 'nullable|boolean',
            'service_category_id'          => 'required|exists:service_categories,id',
            'service_category_form_id'     => 'nullable|exists:service_category_forms,id',
            'property_type_id'             => 'nullable|exists:property_types,id',
            'user_id'                      => 'required|exists:users,id',
            'city_id'                      => 'required|exists:cities,id',
            'views'                        => 'nullable|integer',
            'rating'                       => 'nullable|numeric',
            'no_of_rates'                  => 'nullable|integer',
            'no_guests'                    => 'nullable|integer',
            'beds'                         => 'nullable|integer',
            'baths'                        => 'nullable|integer',
            'booking_rules'                => 'nullable|string',
            'cancelation_rules'            => 'nullable|string',
            'cancelation_policy_id'        => 'nullable|exists:cancellation_policies,id',
            'short_term_policy_id'         => 'nullable|exists:cancellation_policies,id',
            'long_term_policy_id'          => 'nullable|exists:cancellation_policies,id',
            'include_commission_daily'     => 'nullable|boolean',
            'include_commission_weekly'    => 'nullable|boolean',
            'include_commission_monthly'   => 'nullable|boolean',
            'tourism_permit_number'        => 'nullable|string',
            'tourism_permit_document'      => 'nullable|file|mimes:pdf,doc,docx|max:5120',
        ]);
        // Handle image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('service_category_items', 'public');
        }

        // Handle video upload
        if ($request->hasFile('video')) {
            $data['video'] = $request->file('video')->store('service_category_items', 'public');
        }

        // Handle tourism permit document upload
        if ($request->hasFile('tourism_permit_document')) {
            $data['tourism_permit_document'] = $request->file('tourism_permit_document')->store('tourism_permits', 'public');
        }

        $item = ServiceCategoryItem::create($data);

        // Log the creation
        AuditLog::create([
            'user_id'            => auth()->user()->id,
            'description'        => __(self::NAME_S . '_created'),
            'ip_address'         => $request->getClientIp(),
            'relationmodel_type' => get_class($item),
            'relationmodel_id'   => $item->id,
        ]);

        session()->flash('status', __(self::NAME_S . '_created'));
        return redirect(route(self::NAME . '.index'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $item = ServiceCategoryItem::find($id);
        if ($item) {
            $name                = self::NAME;
            $nameS               = self::NAME_S;
            $serviceCategories   = ServiceCategory::all();
            $users               = User::all();
            $cities              = \App\Models\Admin\City::all();
            $propertyTypes       = \App\Models\Admin\PropertyType::all();
            $cancellationPolicies = \App\Models\Admin\CancellationPolicy::all();
            $serviceCategoryForms = ServiceCategoryForm::all();
            
            return view('content.' . self::NAME . '.edit', compact(
                'name', 'nameS', 'item', 'serviceCategories', 'users', 'cities',
                'propertyTypes', 'cancellationPolicies', 'serviceCategoryForms'
            ));
        }
        abort(404);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $item = ServiceCategoryItem::find($id);
        if ($item) {
            // Step 1: Retrieve Form Fields
            $form = ServiceCategoryForm::with(['formItems.field.options'])
                ->where('service_category_id', $item->service_category_id)
                ->where('type', 2) // Replace with the correct form type
                ->first();

            // Step 2: Generate Validation Rules
            $rules = [
                'title'                        => 'required|string',
                'content'                      => 'nullable|string',
                'image'                        => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'video'                        => 'nullable|file|mimetypes:video/mp4,video/quicktime|max:50000',
                'price'                        => 'required|numeric',
                'weekend_price'                => 'nullable|numeric',
                'week_price'                   => 'nullable|numeric',
                'month_price'                  => 'nullable|numeric',
                'lat'                          => 'nullable|numeric',
                'lon'                          => 'nullable|numeric',
                'address'                      => 'nullable|string',
                'active'                       => 'nullable|boolean',
                'confirmation'                 => 'nullable|boolean',
                'service_category_id'          => 'required|exists:service_categories,id',
                'service_category_form_id'     => 'nullable|exists:service_category_forms,id',
                'property_type_id'             => 'nullable|exists:property_types,id',
                'user_id'                      => 'required|exists:users,id',
                'city_id'                      => 'required|exists:cities,id',
                'views'                        => 'nullable|integer',
                'rating'                       => 'nullable|numeric',
                'no_of_rates'                  => 'nullable|integer',
                'no_guests'                    => 'nullable|integer',
                'beds'                         => 'nullable|integer',
                'baths'                        => 'nullable|integer',
                'booking_rules'                => 'nullable|string',
                'cancelation_rules'            => 'nullable|string',
                'cancelation_policy_id'        => 'nullable|exists:cancellation_policies,id',
                'short_term_policy_id'         => 'nullable|exists:cancellation_policies,id',
                'long_term_policy_id'          => 'nullable|exists:cancellation_policies,id',
                'include_commission_daily'     => 'nullable|boolean',
                'include_commission_weekly'    => 'nullable|boolean',
                'include_commission_monthly'   => 'nullable|boolean',
                'tourism_permit_number'        => 'nullable|string',
                'tourism_permit_document'      => 'nullable|file|mimes:pdf,doc,docx|max:5120',
            ];

            $messages = [];

            foreach ($form->formItems as $formItem) {
                $field = $formItem->field;
                if ($field) {
                    $key = 'formfield.' . $formItem->id;

                    // Add required rule if the field is mandatory
                    if ($field->required) {
                        $rules[$key]                  = 'required';
                        $messages[$key . '.required'] = 'The ' . $field->{'title_' . app()->getLocale()} . ' field is required.';
                    }

                    // Add type-specific rules
                    switch ($field->type) {
                        case 7:                                                           // Checkbox
                            $rules[$key]        = 'array';                                    // Ensure it's an array
                            $rules[$key . '.*'] = 'exists:service_category_field_options,id'; // Validate option IDs
                            break;
                        case 8:                                                    // Radio
                            $rules[$key] = 'exists:service_category_field_options,id'; // Validate option ID
                            break;
                        case 1: // Text
                            $rules[$key] = 'string|max:255';
                            break;
                        case 2: // Numeric
                        case 3: // Numeric Decimal
                            $rules[$key] = 'numeric';
                            break;
                        case 4:                  // Phone
                            $rules[$key] = 'string'; //|regex:/^[0-9]{10,15}$/';
                            break;
                        case 5: // Password Text
                        case 6: // Password Numeric
                            $rules[$key] = 'string|min:8';
                            break;
                        case 9: // Text Area
                            $rules[$key] = 'string|max:1000';
                            break;
                        case 10: // Date
                            $rules[$key] = 'date';
                            break;
                        case 11:                 // DateTime
                            $rules[$key] = 'string'; //date_format:Y-m-d H:i:s';
                            break;
                        case 12:                 // Time
                            $rules[$key] = 'string'; //'date_format:H:i:s';
                            break;
                        default:
                            $rules[$key] = 'string';
                            break;
                    }
                }
            }

            // Step 3: Validate the Request Data
            $data = $request->validate($rules, $messages);

            // Handle image upload
            if ($request->hasFile('image')) {
                // Delete old image if exists
                if ($item->image) {
                    Storage::disk('public')->delete($item->image);
                }
                $data['image'] = $request->file('image')->store('service_category_items', 'public');
            }

            // Handle video upload
            if ($request->hasFile('video')) {
                // Delete old video if exists
                if ($item->video) {
                    Storage::disk('public')->delete($item->video);
                }
                $data['video'] = $request->file('video')->store('service_category_items', 'public');
            }

            // Handle tourism permit document upload
            if ($request->hasFile('tourism_permit_document')) {
                // Delete old document if exists
                if ($item->tourism_permit_document) {
                    Storage::disk('public')->delete($item->tourism_permit_document);
                }
                $data['tourism_permit_document'] = $request->file('tourism_permit_document')->store('tourism_permits', 'public');
            }

            // Step 4: Update the Item
            $item->update($data);

            // Step 5: Save Form Field Data
            foreach ($form->formItems as $formItem) {
                $field = $formItem->field;
                if ($field) {
                    $key = 'formfield.' . $formItem->id;

                    // Save other field types
                    $item->fields()->updateOrCreate(
                        ['service_category_field_id' => $field->id],
                        [
                            'value'    => $request->input($key),
                            'type'     => $field->type,
                            'icon'     => $field->icon,
                            'display'  => $field->display,
                            'required' => $field->required,
                        ]
                    );

                }
            }

            // Log the update
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_updated'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_updated'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $item = ServiceCategoryItem::find($id);
        if ($item) {
            // Delete associated files
            if ($item->image) {
                Storage::disk('public')->delete($item->image);
            }
            if ($item->video) {
                Storage::disk('public')->delete($item->video);
            }
            if ($item->tourism_permit_document) {
                Storage::disk('public')->delete($item->tourism_permit_document);
            }

            $item->delete();

            // Log the deletion
            AuditLog::create([
                'user_id'            => auth()->user()->id,
                'description'        => __(self::NAME_S . '_deleted'),
                'ip_address'         => $request->getClientIp(),
                'relationmodel_type' => get_class($item),
                'relationmodel_id'   => $item->id,
            ]);

            session()->flash('status', __(self::NAME_S . '_deleted'));
            return redirect(route(self::NAME . '.index'));
        }
        abort(404);
    }
}
