<?php
namespace App\Models\Admin;

use App\Models\Admin\ServiceCategory;
use App\Models\Admin\ServiceCategoryItemDisablePeriod;
use App\Models\Admin\ServiceCategoryItemDocument;
use App\Models\Admin\ServiceCategoryItemField;
use App\Models\Admin\ServiceCategoryItemOffer;
use App\Models\Admin\ServiceCategoryItemReservation;
use App\Models\Admin\CancellationPolicy;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ServiceCategoryItem extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'city_id',
        'title',   // Single title field
        'content', // Single content field
        'image',   // Image field
        'video',   // Video field
        'price',
        'weekend_price',
        'week_price',
        'month_price',
        'lat',
        'lon',
        'address',
        'active',
        'confirmation', // Reservation confirmation field
        'service_category_id',
        'service_category_form_id',
        'property_type_id',
        'user_id',
        'views',
        'rating',
        'no_of_rates',
        'no_guests',
        'beds',
        'baths',
        'booking_rules',
        'cancelation_rules', // Keep for backward compatibility
        'cancelation_policy_id', // New field for policy reference
        'short_term_policy_id', // Short-term cancellation policy
        'long_term_policy_id', // Long-term cancellation policy
        'include_commission_daily',
        'include_commission_weekly',
        'include_commission_monthly',
        'tourism_permit_number',
        'tourism_permit_document',
    ];

    protected $casts = [
        'active' => 'boolean',
        'confirmation' => 'boolean',
        'price' => 'decimal:2',
        'weekend_price' => 'decimal:2',
        'week_price' => 'decimal:2',
        'month_price' => 'decimal:2',
        'lat' => 'decimal:8',
        'lon' => 'decimal:8',
        'rating' => 'decimal:2',
    ];

    public function incrementViews()
    {
        $this->increment('views');
    }

    public function serviceCategory()
    {
        return $this->belongsTo(ServiceCategory::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function documents()
    {
        return $this->hasMany(ServiceCategoryItemDocument::class);
    }

    public function fields()
    {
        return $this->hasMany(ServiceCategoryItemField::class);
    }

    public function reservations()
    {
        return $this->hasMany(ServiceCategoryItemReservation::class);
    }

    public function offers()
    {
        return $this->hasMany(ServiceCategoryItemOffer::class);
    }

    public function disablePeriods()
    {
        return $this->hasMany(ServiceCategoryItemDisablePeriod::class);
    }

    public function gallery()
    {
        return $this->hasMany(ServiceCategoryItemGallery::class);
    }

    public function facilities()
    {
        // Pivot table: service_category_item_facilities, with service_category_item_id and facilities_id
        return $this->belongsToMany(\App\Models\Admin\Facilities::class, 'service_category_item_facilities', 'service_category_item_id', 'facility_id');
    }

    public function cancellationPolicy()
    {
        return $this->belongsTo(CancellationPolicy::class, 'cancelation_policy_id');
    }

    public function shortTermPolicy()
    {
        return $this->belongsTo(CancellationPolicy::class, 'short_term_policy_id');
    }

    public function longTermPolicy()
    {
        return $this->belongsTo(CancellationPolicy::class, 'long_term_policy_id');
    }

    public function getVideoAttribute($value)
    {
        return $value ? url('storage/' . $value) : null;
    }

    public function getImageAttribute($value)
    {
        return $value ? url('storage/' . $value) : null;
    }
}
