class EndPoints {
  //********  base url
  static const String baserUrl = "https://backend.gatherpoint.sa";

  // Auth endpoints
  static const String login = '/api/client/login';
  static const String validateOTP = '/api/client/validate_otp';
  static const String resendOTP = '/api/client/resend_otp';
  static const String loginToken = '/api/client/login_token';
  static const String loginGuest = '/api/client/login_guest';
  static const String validateToken = '/api/client/validate';
  static const String logout = '/api/client/logout';
  static const String clientInfo = '/api/client/info';
  static const String editProfile = '/api/client/edit_profile';
  static const String toggleHosterMode = '/api/client/toggle_hoster_mode';
  static const String deleteAccount = '/api/client/delete_account';
  static const String followList = '/api/client/follow_list';
  static const String follow = '/api/client/follow';
  static const String unfollow = '/api/client/unfollow';

  // Legacy endpoints (to be updated)
  static const String fetchCategories = '/api/products/categories';
  static const String fetchSingleLearn = '/api/learn/single';
  static const String fetchShop = '/api/products/list';

  // Service Categories
  static const String serviceCategoriesList = '/api/service_categories/list?service_category_id=1';

  // Items/Properties
  static const String itemsList = '/api/items/list';
  static const String itemsSearch = '/api/items/search';
  static const String itemsDetail = '/api/items'; // + /{id}
  static const String itemsCreate = '/api/items/create';
  static const String itemsUpdate = '/api/items/update'; // + /{id}
  static const String itemsUploadGallery = '/api/items/upload-gallery'; // + /{id}

  // Host Listings Management
  static const String hostListings = '/api/host/listings';
  static const String hostListingsStats = '/api/host/listings/stats';
  static const String hostListingToggleStatus = '/api/host/listings/toggle-status'; // + /{id}
  static const String hostListingDelete = '/api/host/listings/delete'; // + /{id}
  static const String hostListingDuplicate = '/api/host/listings/duplicate'; // + /{id}

  // Property Creation
  static const String createProperty = '/api/host/properties';
  static const String uploadImage = '/api/upload/image';
  static const String uploadVideo = '/api/upload/video';
  static const String propertyDrafts = '/api/host/property-drafts';
  static const String propertyMetadata = '/api/host/property-metadata';

  // Reservations
  static const String reservationsList = '/api/reservations/list';
  static const String reservationsCheck = '/api/reservations/check';
  static const String reservationsCreate = '/api/reservations/create';

  // Payments
  static const String paymentInitiate = '/api/payments/initiate';
  static const String paymentStatus = '/api/payments/status';
  static const String paymentUrwayCallback = '/api/payments/urway/callback';

  // Facilities
  static const String facilitiesList = '/api/facilities/list';

  // Property Types
  static const String propertyTypesList = '/api/property_types/list';

  // Cancellation Policies
  static const String cancellationPoliciesList = '/api/cancellation-policies';
  static const String cancellationPolicyDetail = '/api/cancellation-policies'; // + /{id}

  // Favorites
  static const String favoritesList = '/api/favorite/list';
  static const String favoritesSet = '/api/favorite/set';

  // General
  static const String citiesGeo = '/api/general/cities';
  static const String settings = '/api/settings';

//******* routes
}
