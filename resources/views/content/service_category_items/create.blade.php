@php
    $configData = Helper::appClasses();
@endphp

@extends('layouts/layoutMaster')

@section('title', __('add') . ' ' . __($nameS))

@section('vendor-style')
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/formvalidation/dist/css/formValidation.min.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/animate-css/animate.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.css') }}" />
@endsection

@section('vendor-script')
    <script src="{{ asset('assets/vendor/libs/moment/moment.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/FormValidation.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/Bootstrap5.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/formvalidation/dist/js/plugins/AutoFocus.min.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/cleavejs/cleave-phone.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/sweetalert2/sweetalert2.js') }}"></script>
@endsection

@push('scripts')
<script>
    $(document).ready(function() {
        $('#service_category_id').on('change', function() {
            var serviceCategoryId = $(this).val();
            var type = 2; // Assuming type is fixed as 2

            if (!serviceCategoryId) {
                $('#fields_and_documents_container').html('');
                return;
            }

            // Get the base URL from the meta tag
            var appUrl = $('meta[name="app-url"]').attr('content');

            // Construct the full URL for the AJAX request
            var url = appUrl + 'settings/service_categories/get-fields-and-documents/' + serviceCategoryId + '/' + type;
            $.ajax({
                url: url,
                method: 'GET',
                success: function(response) {
                    $('#fields_and_documents_container').html(response.html);
                },
                error: function(xhr, status, error) {
                    console.error('Error fetching fields and documents:', error);
                }
            });

        });
    });
</script>
@endpush

@section('content')
    <div class="row g-4">
        <div class="col-12">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="card-title mt-2"> {{ __('add') . ' ' . __($nameS) }}</h4>
                    <a href="{{ route($name . '.index') }}" class="btn btn-outline-primary float-right">
                        <i class="fas fa-arrow-left mr-1"></i> {{ __('back') }}
                    </a>
                </div>
            </div>
            <div class="card">
                <div class="card-body">
                    @include('_partials._errors')
                    <form action="{{ route($name . '.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    <div class="row">
                        <div class="col-12 mb-4">
                            <label class="form-label required" for="title">{{ __('title') }} <i class="required ml-sm">*</i></label>
                            <input type="text" id="title" name="title" class="form-control" placeholder="{{ __('title') }}" value="{{ old('title') }}" required>
                        </div>
                        <div class="col-12 mb-4">
                            <label class="form-label" for="content">{{ __('content') }}</label>
                            <textarea id="content" name="content" class="form-control" placeholder="{{ __('content') }}">{{ old('content') }}</textarea>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="image">{{ __('image') }}</label>
                            <input type="file" id="image" name="image" class="form-control">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="video">{{ __('video') }}</label>
                            <input type="file" id="video" name="video" class="form-control">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="price">{{ __('price') }} <i class="required ml-sm">*</i></label>
                            <input type="number" step="0.01" id="price" name="price" class="form-control" placeholder="{{ __('price') }}" value="{{ old('price') }}" required>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="weekend_price">{{ __('weekend_price') }}</label>
                            <input type="number" step="0.01" id="weekend_price" name="weekend_price" class="form-control" placeholder="{{ __('weekend_price') }}" value="{{ old('weekend_price') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="week_price">{{ __('week_price') }}</label>
                            <input type="number" step="0.01" id="week_price" name="week_price" class="form-control" placeholder="{{ __('week_price') }}" value="{{ old('week_price') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="month_price">{{ __('month_price') }}</label>
                            <input type="number" step="0.01" id="month_price" name="month_price" class="form-control" placeholder="{{ __('month_price') }}" value="{{ old('month_price') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="lat">{{ __('latitude') }}</label>
                            <input type="number" step="0.000001" id="lat" name="lat" class="form-control" placeholder="{{ __('latitude') }}" value="{{ old('lat') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="lon">{{ __('longitude') }}</label>
                            <input type="number" step="0.000001" id="lon" name="lon" class="form-control" placeholder="{{ __('longitude') }}" value="{{ old('lon') }}">
                        </div>
                        <div class="col-12 mb-4">
                            <label class="form-label" for="address">{{ __('address') }}</label>
                            <input type="text" id="address" name="address" class="form-control" placeholder="{{ __('address') }}" value="{{ old('address') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="city_id">{{ __('city') }} <i class="required ml-sm">*</i></label>
                            <select id="city_id" name="city_id" class="form-control" required>
                                <option value="">{{ __('select_city') }}</option>
                                @foreach($cities as $city)
                                    <option value="{{ $city->id }}" {{ old('city_id') == $city->id ? 'selected' : '' }}>{{ $city->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="property_type_id">{{ __('property_type') }}</label>
                            <select id="property_type_id" name="property_type_id" class="form-control">
                                <option value="">{{ __('select_property_type') }}</option>
                                @foreach($propertyTypes as $propertyType)
                                    <option value="{{ $propertyType->id }}" {{ old('property_type_id') == $propertyType->id ? 'selected' : '' }}>{{ $propertyType->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="service_category_form_id">{{ __('service_category_form') }}</label>
                            <select id="service_category_form_id" name="service_category_form_id" class="form-control">
                                <option value="">{{ __('select_form') }}</option>
                                @foreach($serviceCategoryForms as $form)
                                    <option value="{{ $form->id }}" {{ old('service_category_form_id') == $form->id ? 'selected' : '' }}>{{ $form->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="no_guests">{{ __('no_guests') }}</label>
                            <input type="number" id="no_guests" name="no_guests" class="form-control" placeholder="{{ __('no_guests') }}" value="{{ old('no_guests') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="beds">{{ __('beds') }}</label>
                            <input type="number" id="beds" name="beds" class="form-control" placeholder="{{ __('beds') }}" value="{{ old('beds') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="baths">{{ __('baths') }}</label>
                            <input type="number" id="baths" name="baths" class="form-control" placeholder="{{ __('baths') }}" value="{{ old('baths') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="views">{{ __('views') }}</label>
                            <input type="number" id="views" name="views" class="form-control" placeholder="{{ __('views') }}" value="{{ old('views', 0) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="rating">{{ __('rating') }}</label>
                            <input type="number" step="0.1" min="0" max="5" id="rating" name="rating" class="form-control" placeholder="{{ __('rating') }}" value="{{ old('rating') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="no_of_rates">{{ __('no_of_rates') }}</label>
                            <input type="number" id="no_of_rates" name="no_of_rates" class="form-control" placeholder="{{ __('no_of_rates') }}" value="{{ old('no_of_rates', 0) }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="tourism_permit_number">{{ __('tourism_permit_number') }}</label>
                            <input type="text" id="tourism_permit_number" name="tourism_permit_number" class="form-control" placeholder="{{ __('tourism_permit_number') }}" value="{{ old('tourism_permit_number') }}">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="tourism_permit_document">{{ __('tourism_permit_document') }}</label>
                            <input type="file" id="tourism_permit_document" name="tourism_permit_document" class="form-control" accept=".pdf,.doc,.docx">
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="cancelation_policy_id">{{ __('cancelation_policy') }}</label>
                            <select id="cancelation_policy_id" name="cancelation_policy_id" class="form-control">
                                <option value="">{{ __('select_cancelation_policy') }}</option>
                                @foreach($cancellationPolicies as $policy)
                                    <option value="{{ $policy->id }}" {{ old('cancelation_policy_id') == $policy->id ? 'selected' : '' }}>{{ $policy->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="short_term_policy_id">{{ __('short_term_policy') }}</label>
                            <select id="short_term_policy_id" name="short_term_policy_id" class="form-control">
                                <option value="">{{ __('select_short_term_policy') }}</option>
                                @foreach($cancellationPolicies as $policy)
                                    <option value="{{ $policy->id }}" {{ old('short_term_policy_id') == $policy->id ? 'selected' : '' }}>{{ $policy->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label" for="long_term_policy_id">{{ __('long_term_policy') }}</label>
                            <select id="long_term_policy_id" name="long_term_policy_id" class="form-control">
                                <option value="">{{ __('select_long_term_policy') }}</option>
                                @foreach($cancellationPolicies as $policy)
                                    <option value="{{ $policy->id }}" {{ old('long_term_policy_id') == $policy->id ? 'selected' : '' }}>{{ $policy->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-12 mb-4">
                            <label class="form-label" for="booking_rules">{{ __('booking_rules') }}</label>
                            <textarea id="booking_rules" name="booking_rules" class="form-control" placeholder="{{ __('booking_rules') }}">{{ old('booking_rules') }}</textarea>
                        </div>
                        <div class="col-12 mb-4">
                            <label class="form-label" for="cancelation_rules">{{ __('cancelation_rules') }}</label>
                            <textarea id="cancelation_rules" name="cancelation_rules" class="form-control" placeholder="{{ __('cancelation_rules') }}">{{ old('cancelation_rules') }}</textarea>
                        </div>
                        <div class="col-4 mb-4">
                            <div class="form-check">
                                <input type="checkbox" id="active" name="active" class="form-check-input" value="1" {{ old('active') ? 'checked' : '' }}>
                                <label class="form-check-label" for="active">{{ __('active') }}</label>
                            </div>
                        </div>
                        <div class="col-4 mb-4">
                            <div class="form-check">
                                <input type="checkbox" id="confirmation" name="confirmation" class="form-check-input" value="1" {{ old('confirmation') ? 'checked' : '' }}>
                                <label class="form-check-label" for="confirmation">{{ __('confirmation') }}</label>
                            </div>
                        </div>
                        <div class="col-4 mb-4">
                            <div class="form-check">
                                <input type="checkbox" id="include_commission_daily" name="include_commission_daily" class="form-check-input" value="1" {{ old('include_commission_daily') ? 'checked' : '' }}>
                                <label class="form-check-label" for="include_commission_daily">{{ __('include_commission_daily') }}</label>
                            </div>
                        </div>
                        <div class="col-4 mb-4">
                            <div class="form-check">
                                <input type="checkbox" id="include_commission_weekly" name="include_commission_weekly" class="form-check-input" value="1" {{ old('include_commission_weekly') ? 'checked' : '' }}>
                                <label class="form-check-label" for="include_commission_weekly">{{ __('include_commission_weekly') }}</label>
                            </div>
                        </div>
                        <div class="col-4 mb-4">
                            <div class="form-check">
                                <input type="checkbox" id="include_commission_monthly" name="include_commission_monthly" class="form-check-input" value="1" {{ old('include_commission_monthly') ? 'checked' : '' }}>
                                <label class="form-check-label" for="include_commission_monthly">{{ __('include_commission_monthly') }}</label>
                            </div>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="service_category_id">{{ __('service_category') }} <i class="required ml-sm">*</i></label>
                            <select id="service_category_id" name="service_category_id" class="form-control" required>
                                <option value="">{{ __('select_service_category') }}</option>
                                @foreach($serviceCategories as $category)
                                    <option value="{{ $category->id }}" {{ old('service_category_id') == $category->id ? 'selected' : '' }}>{{ $category->{'title_' . __('lang')} }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-6 mb-4">
                            <label class="form-label required" for="user_id">{{ __('user') }} <i class="required ml-sm">*</i></label>
                            <select id="user_id" name="user_id" class="form-control" required>
                                <option value="">{{ __('select_user') }}</option>
                                @foreach($users as $user)
                                    <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>{{ $user->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div id="fields_and_documents_container">
                            <!-- Fields and documents will be loaded here dynamically -->
                        </div>
                        <div class="col-12 text-center mt-4">
                            <button type="submit" class="btn btn-primary">{{ __('submit') }}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection
