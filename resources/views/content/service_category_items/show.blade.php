@extends('layouts.layoutMaster')

@section('title', __('Service Category Item Details'))

@section('content')
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header">
                <h4 class="mb-0">{{ __('Service Category Item Details') }}</h4>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">ID</dt>
                    <dd class="col-sm-9">{{ $item->id }}</dd>
                    <dt class="col-sm-3">Title</dt>
                    <dd class="col-sm-9">{{ $item->title }}</dd>
                    <dt class="col-sm-3">Content</dt>
                    <dd class="col-sm-9">{{ $item->content }}</dd>
                    <dt class="col-sm-3">Category</dt>
                    <dd class="col-sm-9">{{ $item->serviceCategory->name ?? '-' }}</dd>
                    <dt class="col-sm-3">User</dt>
                    <dd class="col-sm-9">{{ $item->user->name ?? '-' }}</dd>
                    <dt class="col-sm-3">Active</dt>
                    <dd class="col-sm-9">
                        @if($item->active)
                            <span class="badge bg-success">{{ __('Approved') }}</span>
                        @else
                            <span class="badge bg-warning text-dark">{{ __('Pending') }}</span>
                        @endif
                    </dd>
                    <dt class="col-sm-3">Created At</dt>
                    <dd class="col-sm-9">{{ $item->created_at }}</dd>
                    <dt class="col-sm-3">Updated At</dt>
                    <dd class="col-sm-9">{{ $item->updated_at }}</dd>
                    <dt class="col-sm-3">Property Type</dt>
                    <dd class="col-sm-9">{{ $item->property_type_id ? optional($item->propertyType)->name : '-' }}</dd>
                    <dt class="col-sm-3">City</dt>
                    <dd class="col-sm-9">{{ optional($item->city)->name ?? '-' }}</dd>
                    <dt class="col-sm-3">Image</dt>
                    <dd class="col-sm-9">@if($item->image)<img src="{{ $item->image }}" alt="Image" style="max-width:150px;">@else - @endif</dd>
                    <dt class="col-sm-3">Video</dt>
                    <dd class="col-sm-9">@if($item->video)<a href="{{ $item->video }}" target="_blank">View Video</a>@else - @endif</dd>
                    <dt class="col-sm-3">Price</dt>
                    <dd class="col-sm-9">{{ $item->price ?? '-' }}</dd>
                    <dt class="col-sm-3">Weekend Price</dt>
                    <dd class="col-sm-9">{{ $item->weekend_price ?? '-' }}</dd>
                    <dt class="col-sm-3">Week Price</dt>
                    <dd class="col-sm-9">{{ $item->week_price ?? '-' }}</dd>
                    <dt class="col-sm-3">Month Price</dt>
                    <dd class="col-sm-9">{{ $item->month_price ?? '-' }}</dd>
                    <dt class="col-sm-3">Latitude</dt>
                    <dd class="col-sm-9">{{ $item->lat ?? '-' }}</dd>
                    <dt class="col-sm-3">Longitude</dt>
                    <dd class="col-sm-9">{{ $item->lon ?? '-' }}</dd>
                    <dt class="col-sm-3">Address</dt>
                    <dd class="col-sm-9">{{ $item->address ?? '-' }}</dd>
                    <dt class="col-sm-3">Confirmation</dt>
                    <dd class="col-sm-9">{{ $item->confirmation ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-3">Form</dt>
                    <dd class="col-sm-9">{{ $item->service_category_form_id ?? '-' }}</dd>
                    <dt class="col-sm-3">Views</dt>
                    <dd class="col-sm-9">{{ $item->views ?? '-' }}</dd>
                    <dt class="col-sm-3">Rating</dt>
                    <dd class="col-sm-9">{{ $item->rating ?? '-' }}</dd>
                    <dt class="col-sm-3">No. of Rates</dt>
                    <dd class="col-sm-9">{{ $item->no_of_rates ?? '-' }}</dd>
                    <dt class="col-sm-3">No. of Guests</dt>
                    <dd class="col-sm-9">{{ $item->no_guests ?? '-' }}</dd>
                    <dt class="col-sm-3">Beds</dt>
                    <dd class="col-sm-9">{{ $item->beds ?? '-' }}</dd>
                    <dt class="col-sm-3">Baths</dt>
                    <dd class="col-sm-9">{{ $item->baths ?? '-' }}</dd>
                    <dt class="col-sm-3">Booking Rules</dt>
                    <dd class="col-sm-9">{{ $item->booking_rules ?? '-' }}</dd>
                    <dt class="col-sm-3">Cancelation Rules</dt>
                    <dd class="col-sm-9">{{ $item->cancelation_rules ?? '-' }}</dd>
                    <dt class="col-sm-3">Cancelation Policy</dt>
                    <dd class="col-sm-9">{{ optional($item->cancellationPolicy)->name ?? '-' }}</dd>
                    <dt class="col-sm-3">Short Term Policy</dt>
                    <dd class="col-sm-9">{{ optional($item->shortTermPolicy)->name ?? '-' }}</dd>
                    <dt class="col-sm-3">Long Term Policy</dt>
                    <dd class="col-sm-9">{{ optional($item->longTermPolicy)->name ?? '-' }}</dd>
                    <dt class="col-sm-3">Include Commission Daily</dt>
                    <dd class="col-sm-9">{{ $item->include_commission_daily ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-3">Include Commission Weekly</dt>
                    <dd class="col-sm-9">{{ $item->include_commission_weekly ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-3">Include Commission Monthly</dt>
                    <dd class="col-sm-9">{{ $item->include_commission_monthly ? __('Yes') : __('No') }}</dd>
                    <dt class="col-sm-3">Tourism Permit Number</dt>
                    <dd class="col-sm-9">{{ $item->tourism_permit_number ?? '-' }}</dd>
                    <dt class="col-sm-3">Tourism Permit Document</dt>
                    <dd class="col-sm-9">@if($item->tourism_permit_document)<a href="{{ asset('storage/'.$item->tourism_permit_document) }}" target="_blank">{{ __('View Document') }}</a>@else - @endif</dd>
                    <dt class="col-sm-3">Gallery</dt>
                    <dd class="col-sm-9">
                        @if($item->gallery && $item->gallery->count())
                            <div class="d-flex flex-wrap gap-2">
                                @foreach($item->gallery as $gallery)
                                    <a href="{{ asset($gallery->image) }}" target="_blank">
                                        <img src="{{ asset($gallery->image) }}" alt="Gallery Image" style="max-width:80px; max-height:80px; border-radius:6px; border:1px solid #ddd;">
                                    </a>
                                @endforeach
                            </div>
                        @else
                            -
                        @endif
                    </dd>
                    <dt class="col-sm-3">Facilities</dt>
                    <dd class="col-sm-9">
                        @if($item->facilities && $item->facilities->count())
                            <ul class="list-inline mb-0">
                                @foreach($item->facilities as $facility)
                                    <li class="list-inline-item badge bg-info text-dark mb-1">{{ $facility->name ?? $facility->title_en ?? $facility->title ?? '-' }}</li>
                                @endforeach
                            </ul>
                        @else
                            -
                        @endif
                    </dd>
                    <!-- Add more fields as needed -->
                </dl>
                @if(!$item->active)
                <div class="mt-4">
                    <form method="POST" action="{{ route('service_category_items.approve', $item->id) }}" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-success">{{ __('Approve') }}</button>
                    </form>
                    <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#declineModal">{{ __('Decline') }}</button>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Decline Modal -->
<div class="modal fade" id="declineModal" tabindex="-1" aria-labelledby="declineModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <form method="POST" action="{{ route('service_category_items.decline', $item->id) }}">
        @csrf
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="declineModalLabel">{{ __('Decline Item') }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div class="modal-body">
            <div class="mb-3">
              <label for="decline_reason" class="form-label">{{ __('Reason for Decline') }}</label>
              <textarea class="form-control" id="decline_reason" name="decline_reason" rows="3" required></textarea>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
            <button type="submit" class="btn btn-danger">{{ __('Decline') }}</button>
          </div>
        </div>
    </form>
  </div>
</div>
@endsection
