@extends('layouts.admin')

@section('content')
<div class="container">
    <h1>Service Category Item Details</h1>
    <div class="card mb-4">
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-3">ID</dt>
                <dd class="col-sm-9">{{ $item->id }}</dd>
                <dt class="col-sm-3">Title</dt>
                <dd class="col-sm-9">{{ $item->title }}</dd>
                <dt class="col-sm-3">Content</dt>
                <dd class="col-sm-9">{{ $item->content }}</dd>
                <dt class="col-sm-3">Category</dt>
                <dd class="col-sm-9">{{ $item->serviceCategory->name ?? '-' }}</dd>
                <dt class="col-sm-3">User</dt>
                <dd class="col-sm-9">{{ $item->user->name ?? '-' }}</dd>
                <dt class="col-sm-3">Active</dt>
                <dd class="col-sm-9">
                    @if($item->active)
                        <span class="badge bg-success">Approved</span>
                    @else
                        <span class="badge bg-warning text-dark">Pending</span>
                    @endif
                </dd>
                <dt class="col-sm-3">Created At</dt>
                <dd class="col-sm-9">{{ $item->created_at }}</dd>
                <dt class="col-sm-3">Updated At</dt>
                <dd class="col-sm-9">{{ $item->updated_at }}</dd>
                <!-- Add more fields as needed -->
            </dl>
            @if(!$item->active)
            <div class="mt-4">
                <form method="POST" action="{{ route('service_category_items.approve', $item->id) }}" class="d-inline">
                    @csrf
                    <button type="submit" class="btn btn-success">Approve</button>
                </form>
                <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#declineModal">Decline</button>
            </div>
            @endif
        </div>
    </div>

    <!-- Decline Modal -->
    <div class="modal fade" id="declineModal" tabindex="-1" aria-labelledby="declineModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <form method="POST" action="{{ route('service_category_items.decline', $item->id) }}">
            @csrf
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="declineModalLabel">Decline Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
              </div>
              <div class="modal-body">
                <div class="mb-3">
                  <label for="decline_reason" class="form-label">Reason for Decline</label>
                  <textarea class="form-control" id="decline_reason" name="decline_reason" rows="3" required></textarea>
                </div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-danger">Decline</button>
              </div>
            </div>
        </form>
      </div>
    </div>
</div>
@endsection
